import { useTheme } from '@mui/material';
import { useQueryClient } from '@tanstack/react-query';
import React, { useState, useCallback, useRef, useEffect } from 'react';
import { useParams } from 'src/routes/hooks';
import { useChatApi } from 'src/services/api/use-chat-api';
import { taskEndpoints, useTasksApi } from 'src/services/api/use-task-api';

// Define interfaces for EventStream data
interface ChatMessage {
  id: string;
  content: string;
  role: 'user' | 'agent';
  timestamp: Date;
  status?: 'started' | 'processing' | 'completed';
  source?: string;
}

interface EventStreamData {
  status: 'started' | 'processing' | 'completed';
  data:
    | {
        source?: string;
        message?: string;
      }
    | string;
  timestamp: string;
}

const useAgentsChat = () => {
  const theme = useTheme();
  const [message, setMessage] = useState('');
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const [isNewChat, setIsNewChat] = useState(false);
  const [chatMessages, setChatMessages] = useState<ChatMessage[]>([]);
  const [isAgentThinking, setIsAgentThinking] = useState(false);
  const [currentAgentMessage, setCurrentAgentMessage] = useState('');
  const { agentId, cid } = useParams();
  const queryClient = useQueryClient();
  const eventSourceRef = useRef<EventSource | null>(null);

  const { useGetChats } = useChatApi();
  const { useGetTasks, useCreateTask } = useTasksApi();
  const { mutate: createTask, isPending: isPendingTask } = useCreateTask(cid!);
  const { data: chatResponse } = useGetChats(agentId!);
  const { data: tasksResponse } = useGetTasks(cid!);

  // Function to generate unique message ID
  const generateMessageId = () => `msg_${Date.now()}_${Math.random().toString(36).slice(2, 11)}`;

  // Function to simulate EventStream (for demo purposes)
  // This simulates the EventStream response you provided:
  // event: chat_message -> data: {"status":"processing","data":{"source":"CustomerServiceTemplate","message":"Hello! How can I assist you today?"}}
  // event: task_result -> data: {"status":"completed","data":"Hello! How can I assist you today!"}
  // event: complete -> data: {"type":"complete"}
  const simulateEventStream = useCallback((_userMessage: string) => {
    setIsAgentThinking(true);
    setCurrentAgentMessage('');

    // Simulate processing state
    setTimeout(() => {
      setCurrentAgentMessage('Hello! How can I assist you today?');
    }, 1000);

    // Simulate completion
    setTimeout(() => {
      // Add the final agent message to chat
      const agentMessage: ChatMessage = {
        id: generateMessageId(),
        content: 'Hello! How can I assist you today?',
        role: 'agent',
        timestamp: new Date(),
        status: 'completed',
        source: 'agent',
      };

      setChatMessages((prev) => [...prev, agentMessage]);
      setCurrentAgentMessage('');
      setIsAgentThinking(false);
    }, 3000);
  }, []);

  // Function to handle EventStream (real implementation)
  const handleEventStream = useCallback(
    (taskId: string) => {
      // Close existing EventSource if any
      if (eventSourceRef.current) {
        eventSourceRef.current.close();
      }

      const eventSource = new EventSource(`/api/chats/${cid}/tasks/${taskId}/stream`);
      eventSourceRef.current = eventSource;

      setIsAgentThinking(true);
      setCurrentAgentMessage('');

      // Handle different event types
      eventSource.addEventListener('chat_message', (event) => {
        try {
          const eventData = JSON.parse(event.data) as EventStreamData;

          if (eventData.status === 'started') {
            // User message started
            console.log('User message started:', eventData);
          } else if (eventData.status === 'processing') {
            // Agent is responding
            const messageContent =
              typeof eventData.data === 'object' ? eventData.data.message : eventData.data;
            if (messageContent) {
              setCurrentAgentMessage(messageContent);
            }
          }
        } catch (error) {
          console.error('Error parsing chat_message event:', error);
        }
      });

      eventSource.addEventListener('task_result', (event) => {
        try {
          const eventData = JSON.parse(event.data) as EventStreamData;

          if (eventData.status === 'completed') {
            const finalMessage = typeof eventData.data === 'string' ? eventData.data : '';

            // Add the final agent message to chat
            const agentMessage: ChatMessage = {
              id: generateMessageId(),
              content: finalMessage,
              role: 'agent',
              timestamp: new Date(),
              status: 'completed',
              source: 'agent',
            };

            setChatMessages((prev) => [...prev, agentMessage]);
            setCurrentAgentMessage('');
            setIsAgentThinking(false);
          }
        } catch (error) {
          console.error('Error parsing task_result event:', error);
        }
      });

      eventSource.addEventListener('complete', (event) => {
        try {
          console.log('Stream completed:', event.data);
          setIsAgentThinking(false);
          eventSource.close();
        } catch (error) {
          console.error('Error parsing complete event:', error);
        }
      });

      eventSource.onerror = (error) => {
        console.error('EventSource error:', error);
        setIsAgentThinking(false);
        eventSource.close();
      };

      return eventSource;
    },
    [cid]
  );

  const handleSendMessage = () => {
    if (!message.trim()) return;

    // Add user message to chat immediately
    const userMessage: ChatMessage = {
      id: generateMessageId(),
      content: message.trim(),
      role: 'user',
      timestamp: new Date(),
      status: 'started',
    };

    setChatMessages((prev) => [...prev, userMessage]);
    const currentMessage = message.trim();
    setMessage('');

    // Create task and handle EventStream
    const body = {
      content: currentMessage,
    };

    // DEMO MODE: Using simulation for now
    // To switch to real EventStream, comment out the line below and uncomment the createTask block
    simulateEventStream(currentMessage);

    // REAL IMPLEMENTATION: Uncomment this block when backend EventStream is ready
    /*
    createTask(body, {
      onSuccess: (data) => {
        queryClient.invalidateQueries({
          queryKey: [taskEndpoints.list(cid!) + 'list'],
        });

        // Start EventStream for this task
        // The EventStream endpoint should be: `/api/chats/${cid}/tasks/${data.id}/stream`
        if (data?.id) {
          handleEventStream(data.id);
        }
      },
    });
    */
  };

  const handleKeyPress = (event: React.KeyboardEvent) => {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault();
      handleSendMessage();
    }
  };

  const handleNewChat = () => {
    setIsNewChat(true);
    setSidebarOpen(false);
    setMessage('');
  };

  const toggleSidebar = () => {
    setSidebarOpen(!sidebarOpen);
  };

  // Cleanup EventSource on unmount
  useEffect(() => {
    return () => {
      if (eventSourceRef.current) {
        eventSourceRef.current.close();
      }
    };
  }, []);

  return {
    theme,
    message,
    setMessage,
    isNewChat,
    setIsNewChat,
    sidebarOpen,
    setSidebarOpen,
    handleSendMessage,
    handleKeyPress,
    handleNewChat,
    toggleSidebar,
    chatResponse,
    tasksResponse,
    isPendingTask,
    chatMessages,
    isAgentThinking,
    currentAgentMessage,
  };
};

export default useAgentsChat;
