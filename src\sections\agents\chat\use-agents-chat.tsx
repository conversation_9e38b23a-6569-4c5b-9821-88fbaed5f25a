import { useTheme } from '@mui/material';
import { useQueryClient } from '@tanstack/react-query';
import React, { useState } from 'react';
import { useParams } from 'src/routes/hooks';
import { useChatApi } from 'src/services/api/use-chat-api';
import { taskEndpoints, useTasksApi } from 'src/services/api/use-task-api';

const useAgentsChat = () => {
  const theme = useTheme();
  const [message, setMessage] = useState('');
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const [isNewChat, setIsNewChat] = useState(false);
  const { agentId, cid } = useParams();
  const queryClient = useQueryClient();

  const { useGetChats } = useChatApi();
  const { useGetTasks, useCreateTask } = useTasksApi();
  const { mutate: createTask, isPending: isPendingTask } = useCreateTask(cid!);
  const { data: chatResponse } = useGetChats(agentId!);
  const { data: tasksResponse } = useGetTasks(cid!);

  const handleSendMessage = () => {
    const body = {
      content: message,
    };
    createTask(body, {
      onSuccess: (data) => {
        queryClient.invalidateQueries({
          queryKey: [taskEndpoints.list(cid!) + 'list'],
        });
        console.log('data on success', data);
      },
    });
    if (message.trim()) {
      // Handle sending message
      console.log('Sending message:', message);
      setMessage('');
    }
  };

  const handleKeyPress = (event: React.KeyboardEvent) => {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault();
      handleSendMessage();
    }
  };

  const handleNewChat = () => {
    setIsNewChat(true);
    setSidebarOpen(false);
    setMessage('');
  };

  const toggleSidebar = () => {
    setSidebarOpen(!sidebarOpen);
  };
  return {
    theme,
    message,
    setMessage,
    isNewChat,
    setIsNewChat,
    sidebarOpen,
    setSidebarOpen,
    handleSendMessage,
    handleKeyPress,
    handleNewChat,
    toggleSidebar,
    chatResponse,
    tasksResponse,
    isPendingTask,
  };
};

export default useAgentsChat;
