import React, { useState } from 'react';
import {
  Box,
  Typography,
  Button,
  Card,
  Avatar,
  AvatarGroup,
  Chip,
  IconButton,
  TextField,
  InputAdornment,
  Stack,
  Divider,
  useTheme,
} from '@mui/material';
import { Iconify } from 'src/components/iconify';
import { ChatRes } from 'src/services/api/use-chat-api';
import useAgentsChat from './use-agents-chat';
import { ChatEvent, ChatMessage } from './task-result';

// ----------------------------------------------------------------------

interface ChatHistoryItem {
  id: string;
  title: string;
  taskCount: number;
  status: 'active' | 'completed';
  timestamp: string;
}

interface TaskItem {
  id: string;
  title: string;
  status: 'completed' | 'in-progress';
  agents: Array<{
    id: string;
    name: string;
    avatar: string;
  }>;
  timestamp: string;
}

// Mock data

// const TASKS: TaskItem[] = [
//   {
//     id: '1',
//     title: 'Send an interview invitation',
//     status: 'completed',
//     agents: [
//       { id: '1', name: 'X Agent', avatar: '/assets/images/avatars/avatar_1.jpg' },
//       { id: '2', name: 'S Agent', avatar: '/assets/images/avatars/avatar_2.jpg' },
//       { id: '3', name: 'G Agent', avatar: '/assets/images/avatars/avatar_3.jpg' },
//     ],
//     timestamp: 'Now',
//   },
//   {
//     id: '2',
//     title: 'Send an interview invitation',
//     status: 'completed',
//     agents: [
//       { id: '1', name: 'X Agent', avatar: '/assets/images/avatars/avatar_1.jpg' },
//       { id: '2', name: 'S Agent', avatar: '/assets/images/avatars/avatar_2.jpg' },
//       { id: '3', name: 'G Agent', avatar: '/assets/images/avatars/avatar_3.jpg' },
//     ],
//     timestamp: 'Now',
//   },
// ];

const AgentsChat = () => {
  const {
    theme,
    message,
    setMessage,
    isNewChat,
    setIsNewChat,
    sidebarOpen,
    setSidebarOpen,
    handleSendMessage,
    handleKeyPress,
    handleNewChat,
    toggleSidebar,
    chatResponse,
    tasksResponse,
    isPendingTask,
    events,
  } = useAgentsChat();

  console.log('chat response tasks response', tasksResponse);

  return (
    <Box
      sx={{
        display: 'flex',
        height: '100vh',
        bgcolor: '#F5F5F5',
      }}
    >
      {/* Left Sidebar - Chat History */}
      {sidebarOpen && (
        <Box
          sx={{
            width: 280,
            bgcolor: '#FFFFFF',
            borderRight: `1px solid #E0E0E0`,
            display: 'flex',
            flexDirection: 'column',
          }}
        >
          {/* Header */}
          <Box sx={{ p: 2, borderBottom: `1px solid ${theme.palette.divider}` }}>
            <Box
              sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}
            >
              <Typography variant="h6" fontWeight={600}>
                Chats History
              </Typography>
              <Button
                variant="contained"
                size="small"
                onClick={handleNewChat}
                sx={{
                  bgcolor: '#9C6FE4',
                  '&:hover': { bgcolor: '#8B5CF6' },
                  borderRadius: 2,
                  textTransform: 'none',
                  fontSize: '0.75rem',
                  px: 2,
                }}
              >
                New Chat
              </Button>
            </Box>
          </Box>

          {/* Chat History List */}
          <Box sx={{ flex: 1, overflow: 'auto', p: 2 }}>
            {chatResponse &&
              chatResponse?.chats?.length > 0 &&
              chatResponse?.chats?.map((item) => (
                <Card
                  key={item?.id}
                  sx={{
                    p: 2.5,
                    mb: 2,
                    cursor: 'pointer',
                    border: `1px solid #E0E0E0`,
                    borderRadius: 2,
                    bgcolor: item?.status === 'active' ? '#F8F9FA' : '#FFFFFF',
                    '&:hover': {
                      bgcolor: '#F0F0F0',
                      borderColor: '#9C6FE4',
                    },
                  }}
                  onClick={() => setIsNewChat(false)}
                >
                  <Typography
                    variant="h5"
                    fontWeight={600}
                    sx={{ mb: 1.5, color: '#333', fontSize: '0.875rem' }}
                  >
                    {item.title}
                  </Typography>

                  <Typography variant="body1" sx={{ color: '#666', fontSize: '0.75rem' }}>
                    {item.taskCount || 0} tasks
                  </Typography>
                  <Typography variant="caption" sx={{ color: '#999', fontSize: '0.75rem' }}>
                    {item.createdAt}
                  </Typography>
                </Card>
              ))}
          </Box>
        </Box>
      )}

      {/* Main Chat Area */}
      <Box sx={{ flex: 1, display: 'flex', flexDirection: 'column' }}>
        {/* Chat Header */}
        <Box
          sx={{
            p: 2,
            borderBottom: `1px solid #E0E0E0`,
            bgcolor: '#FFFFFF',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
          }}
        >
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <IconButton size="small" onClick={toggleSidebar}>
              <Iconify icon="eva:menu-2-fill" />
            </IconButton>
            <IconButton size="small">
              <Iconify icon="eva:arrow-back-fill" />
            </IconButton>
            <Typography variant="h6" fontWeight={600} color="#333">
              Team Alpha
            </Typography>
          </Box>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <IconButton size="small">
              <Iconify icon="eva:more-vertical-fill" />
            </IconButton>
            <IconButton size="small">
              <Iconify icon="eva:arrow-forward-fill" />
            </IconButton>
          </Box>
        </Box>

        {/* Chat Content */}
        <Box sx={{ flex: 1, overflow: 'auto', p: 3 }}>
          <ChatMessage events={events as ChatEvent[]} isProcessing={isPendingTask} />;
          {isNewChat ? (
            // New Chat Empty State
            <Box
              sx={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center',
                height: '100%',
                textAlign: 'center',
              }}
            >
              <Box
                sx={{
                  width: 80,
                  height: 80,
                  bgcolor: '#9C6FE4',
                  borderRadius: '50%',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  mb: 3,
                }}
              >
                <Iconify icon="eva:message-circle-outline" sx={{ fontSize: 40, color: 'white' }} />
              </Box>
              <Typography variant="h6" fontWeight={600} color="#333" sx={{ mb: 1 }}>
                Give your agent a task
              </Typography>
            </Box>
          ) : (
            // Chat History with Tasks
            tasksResponse &&
            tasksResponse?.tasks?.length > 0 &&
            tasksResponse?.tasks?.map((task) => (
              <Box key={task.id} sx={{ mb: 3 }}>
                {/* Agent Avatars */}
                {/* <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                  <AvatarGroup max={4} sx={{ '& .MuiAvatar-root': { width: 32, height: 32 } }}>
                    {task.agents.map((agent) => (
                      <Avatar
                        key={agent.id}
                        sx={{
                          width: 32,
                          height: 32,
                          bgcolor: '#333',
                          color: 'white',
                          fontSize: '0.75rem',
                          fontWeight: 600,
                        }}
                      >
                        {agent.name.charAt(0)}
                      </Avatar>
                    ))}
                  </AvatarGroup>
                  <Typography variant="caption" color="text.secondary" sx={{ ml: 1 }}>
                    +3 more
                  </Typography>
                  <Box sx={{ flex: 1 }} />
                  <Typography variant="caption" color="text.secondary">
                    {task.timestamp}
                  </Typography>
                </Box> */}

                {/* Task Card */}
                <Card
                  sx={{
                    p: 3,
                    border: `1px solid #E0E0E0`,
                    borderRadius: 2,
                    bgcolor: '#FFFFFF',
                  }}
                >
                  <Box
                    sx={{
                      display: 'flex',
                      justifyContent: 'space-between',
                      alignItems: 'flex-start',
                    }}
                  >
                    <Box sx={{ flex: 1 }}>
                      <Typography variant="body1" fontWeight={500} sx={{ mb: 2, color: '#333' }}>
                        {task.content}
                      </Typography>
                      <Chip
                        label={task?.metadata?.taskResult?.status}
                        size="medium"
                        variant="filled"
                        color={task?.metadata?.taskResult?.status === 'error' ? 'error' : 'success'}
                      />
                    </Box>
                    <Button
                      variant="outlined"
                      size="small"
                      sx={{
                        textTransform: 'none',
                        borderColor: '#E0E0E0',
                        color: '#666',
                        '&:hover': {
                          borderColor: '#9C6FE4',
                          color: '#9C6FE4',
                        },
                      }}
                    >
                      View Task Details
                    </Button>
                  </Box>
                </Card>
              </Box>
            ))
          )}
        </Box>

        {/* Message Input */}
        <Box
          sx={{
            p: 3,
            borderTop: `1px solid #E0E0E0`,
            bgcolor: '#FFFFFF',
          }}
        >
          {isPendingTask ? (
            <Typography>generating response....</Typography>
          ) : (
            <TextField
              fullWidth
              placeholder="Ask me anything"
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              onKeyDown={handleKeyPress}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <Iconify icon="eva:message-circle-outline" sx={{ color: '#999' }} />
                  </InputAdornment>
                ),
                endAdornment: (
                  <InputAdornment position="end">
                    <IconButton
                      onClick={handleSendMessage}
                      disabled={isPendingTask}
                      sx={{
                        bgcolor: message.trim() ? '#9C6FE4' : 'transparent',
                        color: message.trim() ? 'white' : '#999',
                        '&:hover': {
                          bgcolor: message.trim() ? '#8B5CF6' : '#F5F5F5',
                        },
                      }}
                    >
                      <Iconify icon="eva:paper-plane-fill" />
                    </IconButton>
                  </InputAdornment>
                ),
                sx: {
                  borderRadius: 3,
                  bgcolor: '#F8F9FA',
                  '& .MuiOutlinedInput-notchedOutline': {
                    border: '1px solid #E0E0E0',
                  },
                  '&:hover .MuiOutlinedInput-notchedOutline': {
                    border: '1px solid #9C6FE4',
                  },
                  '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                    border: '2px solid #9C6FE4',
                  },
                },
              }}
            />
          )}
        </Box>
      </Box>
    </Box>
  );
};

export default AgentsChat;
