import React, { useState, useRef, useEffect } from 'react';
import {
  <PERSON>,
  Typography,
  Button,
  Card,
  Avatar,
  AvatarGroup,
  Chip,
  IconButton,
  TextField,
  InputAdornment,
  Stack,
  Divider,
  useTheme,
  GlobalStyles,
} from '@mui/material';
import { Iconify } from 'src/components/iconify';
import { ChatRes } from 'src/services/api/use-chat-api';
import useAgentsChat from './use-agents-chat';

// ----------------------------------------------------------------------

interface ChatHistoryItem {
  id: string;
  title: string;
  taskCount: number;
  status: 'active' | 'completed';
  timestamp: string;
}

interface TaskItem {
  id: string;
  title: string;
  status: 'completed' | 'in-progress';
  agents: Array<{
    id: string;
    name: string;
    avatar: string;
  }>;
  timestamp: string;
}

// Mock data

// const TASKS: TaskItem[] = [
//   {
//     id: '1',
//     title: 'Send an interview invitation',
//     status: 'completed',
//     agents: [
//       { id: '1', name: 'X Agent', avatar: '/assets/images/avatars/avatar_1.jpg' },
//       { id: '2', name: 'S Agent', avatar: '/assets/images/avatars/avatar_2.jpg' },
//       { id: '3', name: 'G Agent', avatar: '/assets/images/avatars/avatar_3.jpg' },
//     ],
//     timestamp: 'Now',
//   },
//   {
//     id: '2',
//     title: 'Send an interview invitation',
//     status: 'completed',
//     agents: [
//       { id: '1', name: 'X Agent', avatar: '/assets/images/avatars/avatar_1.jpg' },
//       { id: '2', name: 'S Agent', avatar: '/assets/images/avatars/avatar_2.jpg' },
//       { id: '3', name: 'G Agent', avatar: '/assets/images/avatars/avatar_3.jpg' },
//     ],
//     timestamp: 'Now',
//   },
// ];

const AgentsChat = () => {
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const {
    theme,
    message,
    setMessage,
    isNewChat,
    setIsNewChat,
    sidebarOpen,
    setSidebarOpen,
    handleSendMessage,
    handleKeyPress,
    handleNewChat,
    toggleSidebar,
    chatResponse,
    tasksResponse,
    isPendingTask,
    chatMessages,
    isAgentThinking,
    currentAgentMessage,
  } = useAgentsChat();

  // Auto-scroll to bottom when messages change
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [chatMessages, isAgentThinking, currentAgentMessage]);

  return (
    <>
      <GlobalStyles
        styles={{
          '@keyframes spin': {
            '0%': {
              transform: 'rotate(0deg)',
            },
            '100%': {
              transform: 'rotate(360deg)',
            },
          },
        }}
      />
      <Box
        sx={{
          display: 'flex',
          height: '100vh',
          bgcolor: '#F5F5F5',
        }}
      >
        {/* Left Sidebar - Chat History */}
        {sidebarOpen && (
          <Box
            sx={{
              width: 280,
              bgcolor: '#FFFFFF',
              borderRight: `1px solid #E0E0E0`,
              display: 'flex',
              flexDirection: 'column',
            }}
          >
            {/* Header */}
            <Box sx={{ p: 2, borderBottom: `1px solid ${theme.palette.divider}` }}>
              <Box
                sx={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  mb: 2,
                }}
              >
                <Typography variant="h6" fontWeight={600}>
                  Chats History
                </Typography>
                <Button
                  variant="contained"
                  size="small"
                  onClick={handleNewChat}
                  sx={{
                    bgcolor: '#9C6FE4',
                    '&:hover': { bgcolor: '#8B5CF6' },
                    borderRadius: 2,
                    textTransform: 'none',
                    fontSize: '0.75rem',
                    px: 2,
                  }}
                >
                  New Chat
                </Button>
              </Box>
            </Box>

            {/* Chat History List */}
            <Box sx={{ flex: 1, overflow: 'auto', p: 2 }}>
              {chatResponse &&
                chatResponse?.chats?.length > 0 &&
                chatResponse?.chats?.map((item) => (
                  <Card
                    key={item?.id}
                    sx={{
                      p: 2.5,
                      mb: 2,
                      cursor: 'pointer',
                      border: `1px solid #E0E0E0`,
                      borderRadius: 2,
                      bgcolor: item?.status === 'active' ? '#F8F9FA' : '#FFFFFF',
                      '&:hover': {
                        bgcolor: '#F0F0F0',
                        borderColor: '#9C6FE4',
                      },
                    }}
                    onClick={() => setIsNewChat(false)}
                  >
                    <Typography
                      variant="h5"
                      fontWeight={600}
                      sx={{ mb: 1.5, color: '#333', fontSize: '0.875rem' }}
                    >
                      {item.title}
                    </Typography>

                    <Typography variant="body1" sx={{ color: '#666', fontSize: '0.75rem' }}>
                      {item.taskCount || 0} tasks
                    </Typography>
                    <Typography variant="caption" sx={{ color: '#999', fontSize: '0.75rem' }}>
                      {item.createdAt}
                    </Typography>
                  </Card>
                ))}
            </Box>
          </Box>
        )}

        {/* Main Chat Area */}
        <Box sx={{ flex: 1, display: 'flex', flexDirection: 'column' }}>
          {/* Chat Header */}
          <Box
            sx={{
              p: 2,
              borderBottom: `1px solid #E0E0E0`,
              bgcolor: '#FFFFFF',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
            }}
          >
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <IconButton size="small" onClick={toggleSidebar}>
                <Iconify icon="eva:menu-2-fill" />
              </IconButton>
              <IconButton size="small">
                <Iconify icon="eva:arrow-back-fill" />
              </IconButton>
              <Typography variant="h6" fontWeight={600} color="#333">
                Team Alpha
              </Typography>
            </Box>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <IconButton size="small">
                <Iconify icon="eva:more-vertical-fill" />
              </IconButton>
              <IconButton size="small">
                <Iconify icon="eva:arrow-forward-fill" />
              </IconButton>
            </Box>
          </Box>

          {/* Chat Content */}
          <Box sx={{ flex: 1, overflow: 'auto', p: 3 }}>
            {chatMessages.length === 0 ? (
              // Empty Chat State
              <Box
                sx={{
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  justifyContent: 'center',
                  height: '100%',
                  textAlign: 'center',
                }}
              >
                <Box
                  sx={{
                    width: 80,
                    height: 80,
                    bgcolor: '#9C6FE4',
                    borderRadius: '50%',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    mb: 3,
                  }}
                >
                  <Iconify
                    icon="eva:message-circle-outline"
                    sx={{ fontSize: 40, color: 'white' }}
                  />
                </Box>
                <Typography variant="h6" fontWeight={600} color="#333" sx={{ mb: 1 }}>
                  Give your agent a task
                </Typography>
              </Box>
            ) : (
              // Chat Messages
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                {chatMessages.map((chatMessage) => (
                  <Box
                    key={chatMessage.id}
                    sx={{
                      display: 'flex',
                      flexDirection: 'column',
                      alignItems: chatMessage.role === 'user' ? 'flex-end' : 'flex-start',
                      mb: 2,
                    }}
                  >
                    {/* Message Bubble */}
                    <Box
                      sx={{
                        maxWidth: '75%',
                        p: 2,
                        borderRadius: 2,
                        bgcolor: chatMessage.role === 'user' ? '#9C6FE4' : '#F5F5F5',
                        color: chatMessage.role === 'user' ? 'white' : '#333',
                        borderTopRightRadius: chatMessage.role === 'user' ? 0 : 2,
                        borderTopLeftRadius: chatMessage.role === 'agent' ? 0 : 2,
                      }}
                    >
                      <Typography variant="body2" sx={{ whiteSpace: 'pre-wrap' }}>
                        {chatMessage.content}
                      </Typography>
                    </Box>

                    {/* Timestamp */}
                    <Typography variant="caption" color="text.secondary" sx={{ mt: 0.5, px: 1 }}>
                      {chatMessage.timestamp.toLocaleTimeString([], {
                        hour: '2-digit',
                        minute: '2-digit',
                      })}
                    </Typography>
                  </Box>
                ))}

                {/* Agent Thinking Indicator */}
                {isAgentThinking && (
                  <Box
                    sx={{
                      display: 'flex',
                      flexDirection: 'column',
                      alignItems: 'flex-start',
                      mb: 2,
                    }}
                  >
                    <Box
                      sx={{
                        maxWidth: '75%',
                        p: 2,
                        borderRadius: 2,
                        bgcolor: '#F5F5F5',
                        color: '#333',
                        borderTopLeftRadius: 0,
                        display: 'flex',
                        alignItems: 'center',
                        gap: 1,
                      }}
                    >
                      <Iconify
                        icon="eva:loader-outline"
                        sx={{ animation: 'spin 1s linear infinite' }}
                      />
                      <Typography variant="body2">
                        {currentAgentMessage || 'Agent is thinking...'}
                      </Typography>
                    </Box>
                  </Box>
                )}

                {/* Scroll anchor */}
                <div ref={messagesEndRef} />
              </Box>
            )}
          </Box>

          {/* Message Input */}
          <Box
            sx={{
              p: 3,
              borderTop: `1px solid #E0E0E0`,
              bgcolor: '#FFFFFF',
            }}
          >
            {isAgentThinking ? (
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, p: 2 }}>
                <Iconify
                  icon="eva:loader-outline"
                  sx={{ animation: 'spin 1s linear infinite', color: '#9C6FE4' }}
                />
                <Typography color="#666">Agent is thinking...</Typography>
              </Box>
            ) : (
              <TextField
                fullWidth
                placeholder="Ask me anything"
                value={message}
                onChange={(e) => setMessage(e.target.value)}
                onKeyDown={handleKeyPress}
                disabled={isPendingTask}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <Iconify icon="eva:message-circle-outline" sx={{ color: '#999' }} />
                    </InputAdornment>
                  ),
                  endAdornment: (
                    <InputAdornment position="end">
                      <IconButton
                        onClick={handleSendMessage}
                        disabled={isPendingTask || isAgentThinking || !message.trim()}
                        sx={{
                          bgcolor:
                            message.trim() && !isPendingTask && !isAgentThinking
                              ? '#9C6FE4'
                              : 'transparent',
                          color:
                            message.trim() && !isPendingTask && !isAgentThinking ? 'white' : '#999',
                          '&:hover': {
                            bgcolor:
                              message.trim() && !isPendingTask && !isAgentThinking
                                ? '#8B5CF6'
                                : '#F5F5F5',
                          },
                        }}
                      >
                        <Iconify icon="eva:paper-plane-fill" />
                      </IconButton>
                    </InputAdornment>
                  ),
                  sx: {
                    borderRadius: 3,
                    bgcolor: '#F8F9FA',
                    '& .MuiOutlinedInput-notchedOutline': {
                      border: '1px solid #E0E0E0',
                    },
                    '&:hover .MuiOutlinedInput-notchedOutline': {
                      border: '1px solid #9C6FE4',
                    },
                    '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                      border: '2px solid #9C6FE4',
                    },
                  },
                }}
              />
            )}
          </Box>
        </Box>
      </Box>
    </>
  );
};

export default AgentsChat;
