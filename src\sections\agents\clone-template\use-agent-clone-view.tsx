import { useTheme } from '@mui/material';
import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useParams } from 'src/routes/hooks';
import { paths } from 'src/routes/paths';
import { useAgentsApi } from 'src/services/api/use-agents-api';
import { useChatApi } from 'src/services/api/use-chat-api';
import { TemplateTool, useTemplatesApi } from 'src/services/api/use-templates-api';

const useAgentCloneView = () => {
  const { id } = useParams();
  const { useGetTemplate } = useTemplatesApi();
  const { useCreateAgents } = useAgentsApi();
  const { useCreateChat } = useChatApi();

  const theme = useTheme();
  const navigate = useNavigate();
  const [toolsExpanded, setToolsExpanded] = useState(true);
  const [requirementsExpanded, setRequirementsExpanded] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [specialRequest, setSpecialRequest] = useState('');
  const [generalRequest, setGeneralRequest] = useState('');
  const [configDialogOpen, setConfigDialogOpen] = useState(false);
  const [selectedTool, setSelectedTool] = useState<TemplateTool | null>(null);

  //   const getStatusColor = (status: string) => {
  //     switch (status) {
  //       case 'install':
  //         return '#16A34A'; // Green
  //       case 'reconfigure':
  //         return '#DC2626'; // Red
  //       case 'configuration-required':
  //         return '#EA580C'; // Orange
  //       default:
  //         return theme.palette.text.secondary;
  //     }
  //   };

  //   const getStatusBgColor = (status: string) => {
  //     switch (status) {
  //       case 'install':
  //         return theme.palette.mode === 'dark' ? 'rgba(22, 163, 74, 0.2)' : '#DCFCE7';
  //       case 'reconfigure':
  //         return theme.palette.mode === 'dark' ? 'rgba(220, 38, 38, 0.2)' : '#FEE2E2';
  //       case 'configuration-required':
  //         return theme.palette.mode === 'dark' ? 'rgba(234, 88, 12, 0.2)' : '#FED7AA';
  //       default:
  //         return theme.palette.action.hover;
  //     }
  //   };
  const { mutate: CreateAgent, isPending: isPendingCreateAgent } = useCreateAgents();

  const { mutate: createChat, isPending: isPendingCreateChat } = useCreateChat();
  const { data: templatesResponseById, isLoading } = useGetTemplate(id as string);

  const filteredTools =
    templatesResponseById?.templateTools?.filter((tool) =>
      tool?.tool?.name.toLowerCase().includes(searchQuery.toLowerCase())
    ) || [];

  const handleConfigureClick = (tool: TemplateTool) => {
    setSelectedTool(tool);
    setConfigDialogOpen(true);
  };

  const handleCloseConfigDialog = () => {
    setConfigDialogOpen(false);
    setSelectedTool(null);
  };

  const handleContinueToTheChat = () => {
    const data = {
      templateId: id && id?.length > 0 ? +id : 0,
      specialRequest,
      existingToolConfigs: [
        {
          toolConfigId: 2,
        },
      ],
    };
    CreateAgent(data as any, {
      onSuccess: (res) => {
        const body = {
          title: 'new chat',
          agentId: res?.data?.id || '',
        };
        createChat(body, {
          onSuccess: (resChat) => {
            navigate(
              paths.dashboard.agents.chat(
                id && id?.length > 0 ? +id : 0,
                res?.data?.id || 0,
                resChat?.data?.id
              )
            );
          },
        });
      },
    });
  };

  return {
    templatesResponseById,
    theme,
    toolsExpanded,
    setToolsExpanded,
    searchQuery,
    setSearchQuery,
    filteredTools,
    // getStatusColor,
    // getStatusBgColor,
    setRequirementsExpanded,
    requirementsExpanded,
    setSpecialRequest,
    specialRequest,
    handleConfigureClick,
    selectedTool,
    configDialogOpen,
    handleCloseConfigDialog,
    isLoading,
    navigate,
    isPending: isPendingCreateChat || isPendingCreateAgent,
    handleContinueToTheChat,
  };
};

export default useAgentCloneView;
