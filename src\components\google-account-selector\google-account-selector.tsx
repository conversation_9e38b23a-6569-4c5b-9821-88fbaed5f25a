import React, { useState } from 'react';
import {
  Dialog,
  DialogContent,
  DialogA<PERSON>,
  <PERSON>,
  Typography,
  Button,
  Stack,
  useTheme,
  IconButton,
} from '@mui/material';
import { GoogleLogin, GoogleOAuthProvider } from '@react-oauth/google';
import { jwtDecode } from 'jwt-decode';
import { Iconify } from 'src/components/iconify';

// ----------------------------------------------------------------------

interface GoogleUser {
  id: string;
  email: string;
  name: string;
  picture?: string;
  given_name?: string;
  family_name?: string;
}

interface GoogleAccountSelectorProps {
  open: boolean;
  onClose: () => void;
  onAccountSelect: (user: GoogleUser) => void;
  selectedAccounts?: GoogleUser[];
}

// ----------------------------------------------------------------------

const GOOGLE_CLIENT_ID = import.meta.env.VITE_GOOGLE_CLIENT_ID || '';

export function GoogleAccountSelector({
  open,
  onClose,
  onAccountSelect,
  selectedAccounts = [],
}: GoogleAccountSelectorProps) {
  const theme = useTheme();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleGoogleSuccess = async (credentialResponse: any) => {
    try {
      setIsLoading(true);
      setError(null);

      if (!credentialResponse.credential) {
        throw new Error('No credential received from Google');
      }

      // Decode the JWT token to get user information
      const decoded: any = jwtDecode(credentialResponse.credential);

      const googleUser: GoogleUser = {
        id: decoded.sub,
        email: decoded.email,
        name: decoded.name,
        picture: decoded.picture,
        given_name: decoded.given_name,
        family_name: decoded.family_name,
      };

      // Check if account is already selected
      const isAlreadySelected = selectedAccounts.some((acc) => acc.email === googleUser.email);

      if (isAlreadySelected) {
        setError('This account is already connected');
        return;
      }

      onAccountSelect(googleUser);
      onClose();
    } catch (err: any) {
      console.error('Google login error:', err);
      setError(err.message || 'Failed to authenticate with Google');
    } finally {
      setIsLoading(false);
    }
  };

  const handleGoogleError = () => {
    setError('Google authentication failed. Please try again.');
  };

  const [showAnotherLogin, setShowAnotherLogin] = useState(false);

  const handleUseAnotherAccount = () => {
    // This will show another Google login button to select a different account
    setError(null);
    setShowAnotherLogin(true);
  };

  const handleClose = () => {
    setError(null);
    setShowAnotherLogin(false);
    onClose();
  };

  if (!GOOGLE_CLIENT_ID) {
    return (
      <Dialog open={open} onClose={handleClose} maxWidth="sm" fullWidth>
        <DialogContent>
          <Box textAlign="center" py={3}>
            <Typography color="error" variant="h6">
              Google Client ID not configured
            </Typography>
            <Typography color="text.secondary" sx={{ mt: 1 }}>
              Please add VITE_GOOGLE_CLIENT_ID to your environment variables
            </Typography>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleClose}>Close</Button>
        </DialogActions>
      </Dialog>
    );
  }

  return (
    <GoogleOAuthProvider clientId={GOOGLE_CLIENT_ID}>
      <Dialog
        open={open}
        onClose={handleClose}
        maxWidth="sm"
        fullWidth
        PaperProps={{
          sx: {
            borderRadius: 2,
            p: 0,
            maxWidth: 450,
          },
        }}
      >
        {/* Google Header */}
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            gap: 1,
            p: 3,
            pb: 2,
            borderBottom: `1px solid ${theme.palette.divider}`,
          }}
        >
          <Box display="flex" alignItems="center" gap={1}>
            <Iconify icon="logos:google" width={24} height={24} />
            <Typography variant="h6" fontWeight={400} color="text.secondary">
              Sign in with Google
            </Typography>
          </Box>
          <IconButton onClick={handleClose} size="small">
            <Iconify icon="eva:close-fill" />
          </IconButton>
        </Box>

        <DialogContent sx={{ p: 3, pt: 2 }}>
          <Box sx={{ textAlign: 'center', mb: 3 }}>
            <Typography variant="h5" fontWeight={400} sx={{ mb: 1 }}>
              Choose an account
            </Typography>
            <Typography variant="body2" color="text.secondary">
              to continue to <span style={{ color: '#1976d2' }}>beyondperimeter.com</span>
            </Typography>
          </Box>

          {error && (
            <Box
              sx={{
                p: 2,
                mb: 2,
                backgroundColor: theme.palette.error.lighter,
                borderRadius: 1,
                border: `1px solid ${theme.palette.error.light}`,
              }}
            >
              <Typography variant="body2" color="error">
                {error}
              </Typography>
            </Box>
          )}

          <Stack spacing={2} alignItems="center">
            {/* Google Login Button */}
            <GoogleLogin
              onSuccess={handleGoogleSuccess}
              onError={handleGoogleError}
              useOneTap={false}
              auto_select={false}
              theme="outline"
              size="large"
              text="signin_with"
              shape="rectangular"
              width="100%"
            />

            {/* Use another account option */}
            {!showAnotherLogin ? (
              <Box
                onClick={handleUseAnotherAccount}
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: 2,
                  p: 2,
                  border: `1px solid ${theme.palette.divider}`,
                  borderRadius: 1,
                  cursor: 'pointer',
                  transition: 'all 0.2s ease',
                  width: '100%',
                  '&:hover': {
                    backgroundColor: theme.palette.action.hover,
                    borderColor: theme.palette.primary.main,
                  },
                }}
              >
                <Box
                  sx={{
                    width: 32,
                    height: 32,
                    borderRadius: '50%',
                    backgroundColor: theme.palette.grey[400],
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}
                >
                  <Iconify icon="eva:person-outline" width={18} height={18} color="white" />
                </Box>
                <Typography variant="body1" fontWeight={500} sx={{ fontSize: '14px' }}>
                  Use another account
                </Typography>
              </Box>
            ) : (
              <Box sx={{ width: '100%' }}>
                <Typography
                  variant="body2"
                  color="text.secondary"
                  sx={{ mb: 2, textAlign: 'center' }}
                >
                  Or sign in with a different account:
                </Typography>
                <GoogleLogin
                  onSuccess={handleGoogleSuccess}
                  onError={handleGoogleError}
                  useOneTap={false}
                  auto_select={false}
                  theme="filled_blue"
                  size="large"
                  text="continue_with"
                  shape="rectangular"
                  width="100%"
                />
              </Box>
            )}

            {/* Privacy Notice */}
            <Box
              sx={{
                mt: 2,
                p: 2,
                backgroundColor: theme.palette.grey[50],
                borderRadius: 1,
                width: '100%',
              }}
            >
              <Typography
                variant="caption"
                color="text.secondary"
                sx={{ fontSize: '12px', lineHeight: 1.4 }}
              >
                To continue, Google will share your name, email address, language preference, and
                profile picture with beyondperimeter.com.
              </Typography>
            </Box>
          </Stack>
        </DialogContent>

        <DialogActions sx={{ p: 3, pt: 1, justifyContent: 'flex-start' }}>
          <Button onClick={handleClose} color="inherit" sx={{ textTransform: 'none' }}>
            ← Back
          </Button>
        </DialogActions>
      </Dialog>
    </GoogleOAuthProvider>
  );
}
