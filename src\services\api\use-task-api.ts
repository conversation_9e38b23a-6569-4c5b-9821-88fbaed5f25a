import axiosInstance from 'src/utils/axios';
import { useApiServices } from 'src/services/hooks/use-api-services';

// Define the API endpoints for Agentss
export const taskEndpoints = {
  list: (cid: string | number) => `/chats/${cid}/tasks`,
  details: '/categories',
};
// Define the Category interface
export type Tasks = {
  id: number;
  chatId: number;
  content: 'Please research the latest market trends';
  senderId: number;
  status: string;
  createdAt: string;
  updatedAt: string;
  metadata: {
    error: string;
    errorTime: string;
    eventType: string;
    taskResult: {
      data: string;
      status: string;
      timestamp: string;
    };
  };
  agentId: 4;
};
export interface TasksResponse {
  tasks: Tasks[];
  total: 1;
}
export interface TasksBody {
  content: string;
}

// Define the API response structure

// Create a hook to use the Agentss API
export const useTasksApi = () => {
  const apiServices = useApiServices({ axiosInstance });

  // Get all Agentss
  const useGetTasks = (cid: string | number) => {
    return apiServices.useGetListService<TasksResponse>({
      url: taskEndpoints.list(cid),
    });
  };

  // Get a single Agents by ID

  // Create a new Agents
  const useCreateTask = (cid: number | string, onSuccess?: (data: any) => void) => {
    return apiServices.usePostService<TasksBody, any>({
      url: taskEndpoints.list(cid),
      onSuccess,
    });
  };

  return {
    useGetTasks,
    useCreateTask,
  };
};
